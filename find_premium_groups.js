// Bento Console <PERSON>t: Find inventory billable groups with "Premium" in the name
// Copy and paste this into the browser console while logged into Bento

databaseConnection.obj.getWhere('inventory_billable_groups', {name: 'Premium'}, function(ib_groups) {
    console.log('Found groups with "Premium" in name:');
    console.log('Total count:', ib_groups.length);
    
    if (ib_groups.length > 0) {
        ib_groups.forEach((group, index) => {
            console.log(`\n--- Group ${index + 1} ---`);
            console.log('ID:', group.id);
            console.log('Name:', group.name);
            if (group.category) console.log('Category:', group.category);
            if (group.created) console.log('Created:', group.created);
            if (group.modified) console.log('Modified:', group.modified);
            console.log('Full object:', group);
        });
    } else {
        console.log('No groups found with "Premium" in the name.');
    }
});
