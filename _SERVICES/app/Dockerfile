FROM 918760427934.dkr.ecr.us-east-2.amazonaws.com/bento-build:latest

RUN apt-get -y --allow-releaseinfo-change update
RUN	apt-get install -y libxml2-dev
RUN docker-php-ext-install soap && docker-php-ext-enable soap

# Copy custom apache config file
COPY ./my-apache2.conf /etc/apache2/sites-enabled/httpd.conf
		        
# Copy app files
COPY ./src /var/www/html/

# Install composer dependancies
RUN cd /var/www/html/api/ && composer install

# Allow writing to temp mpdf directory
RUN cd /var/www/html/api/ && chmod -R 777 tmp-pdf-generation

EXPOSE 80