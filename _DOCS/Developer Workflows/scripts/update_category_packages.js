/**
 * <PERSON>ript to update inventory_group references in copied combinations
 * to point to the copied groups instead of the original groups
 *
 * @param {boolean} dryRun - If true, only logs changes without updating records
 * @param {number} limit - Limit processing to this many combinations (for testing)
 * @param {number} startIndex - Skip this many combinations before starting (default: 0)
 * @param {Array<number>} categoryIds - Array of category IDs to process
 */
function updateCombinationGroupReferences(dryRun = true, limit = 2, startIndex = 0, categoryIds = [20456830]) {
  console.log(`\n========== STARTING GROUP REFERENCE UPDATE PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will update records)'}`);
  console.log(`Processing: ${limit} combinations starting from index ${startIndex}`);
  console.log(`Target Category IDs: ${categoryIds.join(', ')}`);
  console.log(`=============================================================\n`);

  // Process each category ID
  processCategoriesSequentially(categoryIds, 0, dryRun, limit, startIndex);
}

/**
 * Process category IDs one at a time
 */
function processCategoriesSequentially(categoryIds, categoryIndex, dryRun, limit, startIndex) {
  if (categoryIndex >= categoryIds.length) {
    console.log(`\n========== ALL CATEGORIES PROCESSED ==========`);
    return;
  }

  const categoryId = categoryIds[categoryIndex];
  console.log(`\n========== PROCESSING CATEGORY ${categoryIndex + 1}/${categoryIds.length} ==========`);
  console.log(`Category ID: ${categoryId}`);

  // Query combinations for this category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {
    category: categoryId
  }, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}:`);

    combinations.forEach((combo, index) => {
      console.log(`  ${index + 1}. "${combo.name}" (ID: ${combo.id})`);
    });

    console.log(`\n--- Starting detailed analysis of combinations ---`);

    // Process each combination with getById to get full details
    processCombinationDetails(combinations, 0, categoryId, limit, startIndex);
  });
}

/**
 * Process combination details one at a time using getById
 */
function processCombinationDetails(combinations, comboIndex, categoryId, limit, startIndex) {
  if (comboIndex >= combinations.length) {
    console.log(`\n--- Completed detailed analysis for category ${categoryId} ---`);
    return;
  }

  const combo = combinations[comboIndex];
  console.log(`\n========== COMBINATION DETAILS ${comboIndex + 1}/${combinations.length} ==========`);
  console.log(`Getting full details for: "${combo.name}" (ID: ${combo.id})`);

  // Get full combination details using getById
  databaseConnection.obj.getById('inventory_billable_combinations', combo.id, function(fullCombo) {
    console.log(`Full combination data retrieved for "${fullCombo.name}"`);

    // Process items array
    if (fullCombo.items && Array.isArray(fullCombo.items)) {
      console.log(`Found ${fullCombo.items.length} items in combination:`);

      fullCombo.items.forEach((item, itemIndex) => {
        console.log(`\n  Item ${itemIndex + 1}:`);
        console.log(`    ID: ${item.id}`);
        console.log(`    Name: "${item.name || 'Unnamed item'}"`);
        console.log(`    Inventory Group ID: ${item.inventory_group}`);
        console.log(`    Max Selections: ${item.max_selections}`);
        console.log(`    Choices Array Length: ${item.choices ? item.choices.length : 'No choices array'}`);
      });

      // Now get details for each inventory group
      processInventoryGroupDetails(fullCombo.items, 0, fullCombo.name);
    } else {
      console.log(`No items array found in combination "${fullCombo.name}"`);

      // Process next combination
      processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
    }
  });
}

/**
 * Process inventory group details for each item
 */
function processInventoryGroupDetails(items, itemIndex, comboName) {
  if (itemIndex >= items.length) {
    console.log(`\n--- Completed inventory group analysis for "${comboName}" ---`);

    // TEMPORARY RETURN - stop here to review logs
    console.log(`\n🛑 TEMPORARY RETURN - stopping here to review inventory group details`);
    return;
  }

  const item = items[itemIndex];
  console.log(`\n    Getting inventory group details for Item ${itemIndex + 1}:`);

  if (item.inventory_group && item.inventory_group !== 0) {
    databaseConnection.obj.getById('inventory_billable_groups', item.inventory_group, function(group) {
      console.log(`      Group Name: "${group.name}"`);
      console.log(`      Group ID: ${group.id}`);
      console.log(`      Group Category: ${group.category}`);
      console.log(`      Group Description: "${group.description || 'No description'}"`);

      // Process next item
      processInventoryGroupDetails(items, itemIndex + 1, comboName);
    });
  } else {
    console.log(`      No inventory group (ID: ${item.inventory_group})`);

    // Process next item
    processInventoryGroupDetails(items, itemIndex + 1, comboName);
  }
}

/**
 * Process combinations in the target category
 */
function processCombinations(groupMapping, dryRun, limit, startIndex, categoryId) {
  console.log(`\nStep 2: Processing combinations in category ${categoryId}...`);

  // Query combinations using the same structure as the XHR
  const queryObj = {
    category: categoryId,
    paged: {
      page: 0,
      pageLength: 50,
      paged: true,
      sortCol: "name",
      sortDir: "asc",
      sortCast: "string",
      count: true
    }
  };

  databaseConnection.obj.getWhere('inventory_billable_combinations', queryObj, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}.`);

    // Filter to only combinations with "(*)" in name
    const copyCombinations = combinations.filter(combo => combo.name.includes('(*)'));
    console.log(`Found ${copyCombinations.length} combinations with "(*)" in their names.`);

    if (copyCombinations.length === 0) {
      console.warn(`⚠️ WARNING: No combinations with "(*)" found in category ${categoryId}.`);
      return;
    }

    // Apply start index and limit
    const combinationsToProcess = copyCombinations.slice(startIndex, startIndex + limit);

    if (combinationsToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No combinations to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${combinationsToProcess.length} combinations:`);
    combinationsToProcess.forEach((combo, idx) => {
      console.log(`  ${idx+1}. "${combo.name}" (ID: ${combo.id})`);
    });

    // Track statistics
    const stats = {
      combinationsProcessed: 0,
      combinationsUpdated: 0,
      combinationsSkipped: 0,
      groupReferencesFound: 0,
      groupReferencesUpdated: 0,
      groupReferencesNotMapped: 0
    };

    // Process combinations sequentially
    processNextCombinationUpdate(combinationsToProcess, 0, groupMapping, dryRun, stats);
  });
}

/**
 * Process combinations one at a time to avoid server connection issues
 */
function processNextCombinationUpdate(combinations, index, groupMapping, dryRun, stats) {
  if (index >= combinations.length) {
    console.log(`\n========== GROUP REFERENCE UPDATE PROCESS COMPLETE ==========`);
    console.log(`Combinations processed: ${stats.combinationsProcessed}`);
    console.log(`Combinations updated: ${stats.combinationsUpdated}`);
    console.log(`Combinations skipped (no changes needed): ${stats.combinationsSkipped}`);
    console.log(`Group references found: ${stats.groupReferencesFound}`);
    console.log(`Group references updated: ${stats.groupReferencesUpdated}`);
    console.log(`Group references not mapped: ${stats.groupReferencesNotMapped}`);
    console.log(`==============================================================\n`);
    return;
  }

  const combination = combinations[index];
  stats.combinationsProcessed++;

  console.log(`\n========== COMBINATION ${index+1}/${combinations.length} ==========`);
  console.log(`Processing combination: "${combination.name}" (ID: ${combination.id})`);

  // Verify this combination belongs to a category with "(*)"
  if (!combination.name.includes('(*)')) {
    console.warn(`⚠️ SKIPPING: Combination "${combination.name}" does not have "(*)" in name`);
    stats.combinationsSkipped++;
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    return;
  }

  // Clone the combination for modification
  var updatedCombination = _.clone(combination);
  var hasChanges = false;

  // Process items array
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    console.log(`Found ${updatedCombination.items.length} items in combination.`);

    updatedCombination.items.forEach((item, itemIndex) => {
      console.log(`  Processing item ${itemIndex + 1}: "${item.name || 'Unnamed item'}"`);

      // Process choices array within each item
      if (item.choices && Array.isArray(item.choices)) {
        console.log(`    Found ${item.choices.length} choices in item ${itemIndex + 1}.`);

        item.choices.forEach((choice, choiceIndex) => {
          stats.groupReferencesFound++;

          if (choice.inventory_group && choice.inventory_group !== 0) {
            const originalGroupId = choice.inventory_group;
            console.log(`      Choice ${choiceIndex + 1}: inventory_group = ${originalGroupId}`);

            if (groupMapping[originalGroupId]) {
              const newGroupId = groupMapping[originalGroupId];
              choice.inventory_group = newGroupId;
              hasChanges = true;
              stats.groupReferencesUpdated++;
              console.log(`        ✅ UPDATED: ${originalGroupId} → ${newGroupId}`);
            } else {
              stats.groupReferencesNotMapped++;
              console.log(`        ⚠️ NOT MAPPED: Group ID ${originalGroupId} not found in mapping`);
            }
          } else {
            console.log(`      Choice ${choiceIndex + 1}: no inventory_group or inventory_group = 0`);
          }
        });
      } else {
        console.log(`    No choices array found in item ${itemIndex + 1}.`);
      }
    });
  } else {
    console.log(`No items array found in combination.`);
  }

  // Update the combination if changes were made
  if (hasChanges) {
    if (!dryRun) {
      console.log(`Updating combination "${combination.name}"...`);

      databaseConnection.obj.update('inventory_billable_combinations', updatedCombination, function() {
        stats.combinationsUpdated++;
        console.log(`✅ UPDATED: Combination "${combination.name}" (ID: ${combination.id})`);

        // Process the next combination after a short delay
        setTimeout(function() {
          processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
        }, 100);
      });
    } else {
      console.log(`[DRY RUN] Would update combination "${combination.name}" with ${stats.groupReferencesUpdated} group reference changes.`);
      stats.combinationsUpdated++;

      // Process the next combination (no delay needed for dry run)
      processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    }
  } else {
    console.log(`No changes needed for combination "${combination.name}".`);
    stats.combinationsSkipped++;

    // Process the next combination (no delay needed)
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
  }
}
