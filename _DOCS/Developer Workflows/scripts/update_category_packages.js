/**
 * <PERSON>ript to update inventory_group references in copied combinations
 * to point to the copied groups instead of the original groups
 *
 * @param {boolean} dryRun - If true, only logs changes without updating records
 * @param {number} limit - Limit processing to this many combinations (for testing)
 * @param {number} startIndex - Skip this many combinations before starting (default: 0)
 * @param {Array<number>} categoryIds - Array of category IDs to process
 */
function updateCombinationGroupReferences(dryRun = true, limit = 2, startIndex = 0, categoryIds = [20456830]) {
  console.log(`\n========== STARTING GROUP REFERENCE UPDATE PROCESS ==========`);
  console.log(`Mode: ${dryRun ? 'DRY RUN (no changes will be made)' : 'LIVE RUN (will update records)'}`);
  console.log(`Processing: ${limit} combinations starting from index ${startIndex}`);
  console.log(`Target Category IDs: ${categoryIds.join(', ')}`);
  console.log(`=============================================================\n`);

  // Process each category ID
  processCategoriesSequentially(categoryIds, 0, dryRun, limit, startIndex);
}

/**
 * Process category IDs one at a time
 */
function processCategoriesSequentially(categoryIds, categoryIndex, dryRun, limit, startIndex) {
  if (categoryIndex >= categoryIds.length) {
    console.log(`\n========== ALL CATEGORIES PROCESSED ==========`);
    return;
  }

  const categoryId = categoryIds[categoryIndex];
  console.log(`\n========== PROCESSING CATEGORY ${categoryIndex + 1}/${categoryIds.length} ==========`);
  console.log(`Category ID: ${categoryId}`);

  // Query combinations for this category
  databaseConnection.obj.getWhere('inventory_billable_combinations', {
    category: categoryId
  }, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}:`);

    combinations.forEach((combo, index) => {
      console.log(`  ${index + 1}. "${combo.name}" (ID: ${combo.id})`);
    });

    console.log(`\n--- Starting detailed analysis of combinations ---`);

    // Process each combination with getById to get full details
    processCombinationDetails(combinations, 0, categoryId, limit, startIndex);
  });
}

/**
 * Process combination details one at a time using getById
 */
function processCombinationDetails(combinations, comboIndex, categoryId, limit, startIndex) {
  if (comboIndex >= combinations.length) {
    console.log(`\n--- Completed detailed analysis for category ${categoryId} ---`);
    return;
  }

  const combo = combinations[comboIndex];
  console.log(`\n========== COMBINATION DETAILS ${comboIndex + 1}/${combinations.length} ==========`);
  console.log(`Getting full details for: "${combo.name}" (ID: ${combo.id})`);

  // Get full combination details using getById
  databaseConnection.obj.getById('inventory_billable_combinations', combo.id, function(fullCombo) {
    console.log(`Full combination data retrieved for "${fullCombo.name}"`);

    // Process items array and find ALL inventory_group references
    if (fullCombo.items && Array.isArray(fullCombo.items)) {
      console.log(`Found ${fullCombo.items.length} items in combination:`);

      // Collect all inventory_group IDs from the entire structure
      const allInventoryGroupIds = [];

      fullCombo.items.forEach((item, itemIndex) => {
        console.log(`\n  Item ${itemIndex + 1}:`);
        console.log(`    ID: ${item.id}`);
        console.log(`    Name: "${item.name || 'Unnamed item'}"`);
        console.log(`    Max Selections: ${item.max_selections}`);

        // Check for direct inventory_group reference
        if (item.inventory_group && item.inventory_group !== 0) {
          console.log(`    Direct Inventory Group ID: ${item.inventory_group}`);
          allInventoryGroupIds.push({
            id: item.inventory_group,
            location: `items[${itemIndex}].inventory_group`,
            itemIndex: itemIndex,
            choiceIndex: null
          });
        } else {
          console.log(`    Direct Inventory Group ID: ${item.inventory_group || 'None'}`);
        }

        // Check for choices array and inventory_group references within choices
        if (item.choices && Array.isArray(item.choices)) {
          console.log(`    Choices Array Length: ${item.choices.length}`);

          item.choices.forEach((choice, choiceIndex) => {
            console.log(`      Choice ${choiceIndex + 1}:`);
            console.log(`        Choice ID: ${choice.id}`);
            console.log(`        Choice Name: "${choice.name || 'Unnamed choice'}"`);

            if (choice.inventory_group && choice.inventory_group !== 0) {
              console.log(`        Choice Inventory Group ID: ${choice.inventory_group}`);
              allInventoryGroupIds.push({
                id: choice.inventory_group,
                location: `items[${itemIndex}].choices[${choiceIndex}].inventory_group`,
                itemIndex: itemIndex,
                choiceIndex: choiceIndex
              });
            } else {
              console.log(`        Choice Inventory Group ID: ${choice.inventory_group || 'None'}`);
            }
          });
        } else {
          console.log(`    Choices Array: None`);
        }
      });

      console.log(`\n  SUMMARY: Found ${allInventoryGroupIds.length} total inventory_group references:`);
      allInventoryGroupIds.forEach((ref, index) => {
        console.log(`    ${index + 1}. ID ${ref.id} at ${ref.location}`);
      });

      // Now get details for each inventory group and build mapping
      buildGroupMappingForCombination(allInventoryGroupIds, 0, fullCombo, {});
    } else {
      console.log(`No items array found in combination "${fullCombo.name}"`);

      // Process next combination
      processCombinationDetails(combinations, comboIndex + 1, categoryId, limit, startIndex);
    }
  });
}

/**
 * Build group mapping for this combination by finding copy groups for each original group
 */
function buildGroupMappingForCombination(inventoryGroupRefs, refIndex, fullCombo, groupMapping) {
  if (refIndex >= inventoryGroupRefs.length) {
    console.log(`\n--- Completed group mapping analysis for "${fullCombo.name}" ---`);
    console.log(`Group mapping built:`, groupMapping);

    // Now update the combination with the new group IDs
    updateCombinationWithNewGroupIds(fullCombo, groupMapping);
    return;
  }

  const ref = inventoryGroupRefs[refIndex];
  console.log(`\n    Processing group reference ${refIndex + 1}/${inventoryGroupRefs.length}:`);
  console.log(`      Location: ${ref.location}`);
  console.log(`      Group ID: ${ref.id}`);

  // Get the original group details
  databaseConnection.obj.getById('inventory_billable_groups', ref.id, function(originalGroup) {
    console.log(`      Original Group Name: "${originalGroup.name}"`);
    console.log(`      Has "(*)" in name: ${originalGroup.name.includes('(*)')}`);

    if (originalGroup.name.includes('(*)')) {
      // This is already a copy group, no need to map
      console.log(`      ✅ Already a copy group, no mapping needed`);
      buildGroupMappingForCombination(inventoryGroupRefs, refIndex + 1, fullCombo, groupMapping);
    } else {
      // This is an original group, find its copy
      const copyGroupName = originalGroup.name + ' (*)';
      console.log(`      🔍 Looking for copy group: "${copyGroupName}"`);

      databaseConnection.obj.getWhere('inventory_billable_groups', {
        name: copyGroupName
      }, function(copyGroups) {
        if (copyGroups && copyGroups.length > 0) {
          const copyGroup = copyGroups[0];
          groupMapping[originalGroup.id] = copyGroup.id;
          console.log(`      ✅ MAPPING FOUND: "${originalGroup.name}" (${originalGroup.id}) → "${copyGroup.name}" (${copyGroup.id})`);

          // Show detailed comparison
          console.log(`        ORIGINAL GROUP DETAILS:`);
          console.log(`          ID: ${originalGroup.id}`);
          console.log(`          Name: "${originalGroup.name}"`);
          console.log(`        COPY GROUP DETAILS:`);
          console.log(`          ID: ${copyGroup.id}`);
          console.log(`          Name: "${copyGroup.name}"`);
        } else {
          console.log(`      ⚠️ WARNING: No copy group found for "${originalGroup.name}"`);
        }

        // Continue with next reference
        buildGroupMappingForCombination(inventoryGroupRefs, refIndex + 1, fullCombo, groupMapping);
      });
    }
  });
}

/**
 * Update the combination with new group IDs based on the mapping
 */
function updateCombinationWithNewGroupIds(fullCombo, groupMapping) {
  console.log(`\n--- Updating combination "${fullCombo.name}" with new group IDs ---`);

  if (Object.keys(groupMapping).length === 0) {
    console.log(`No group mappings found, nothing to update.`);

    // TEMPORARY RETURN - stop here to review logs
    console.log(`\n🛑 TEMPORARY RETURN - stopping here to review mapping results`);
    return;
  }

  // Clone the combination for modification
  var updatedCombination = _.clone(fullCombo);
  var hasChanges = false;
  var changesLog = [];

  // Update items array
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    updatedCombination.items.forEach((item, itemIndex) => {
      // Update direct inventory_group reference
      if (item.inventory_group && groupMapping[item.inventory_group]) {
        const oldId = item.inventory_group;
        const newId = groupMapping[item.inventory_group];
        item.inventory_group = newId;
        hasChanges = true;
        changesLog.push(`items[${itemIndex}].inventory_group: ${oldId} → ${newId}`);
        console.log(`  ✅ UPDATED: items[${itemIndex}].inventory_group: ${oldId} → ${newId}`);
      }

      // Update choices array inventory_group references
      if (item.choices && Array.isArray(item.choices)) {
        item.choices.forEach((choice, choiceIndex) => {
          if (choice.inventory_group && groupMapping[choice.inventory_group]) {
            const oldId = choice.inventory_group;
            const newId = groupMapping[choice.inventory_group];
            choice.inventory_group = newId;
            hasChanges = true;
            changesLog.push(`items[${itemIndex}].choices[${choiceIndex}].inventory_group: ${oldId} → ${newId}`);
            console.log(`  ✅ UPDATED: items[${itemIndex}].choices[${choiceIndex}].inventory_group: ${oldId} → ${newId}`);
          }
        });
      }
    });
  }

  console.log(`\nSUMMARY: ${hasChanges ? 'Changes made' : 'No changes needed'}`);
  if (hasChanges) {
    console.log(`Changes made:`);
    changesLog.forEach((change, index) => {
      console.log(`  ${index + 1}. ${change}`);
    });

    console.log(`\n[DRY RUN] Would update combination "${fullCombo.name}" (ID: ${fullCombo.id})`);

    // Show verification of what the updated groups would be
    console.log(`\n--- VERIFICATION: Showing updated group details ---`);
    verifyUpdatedGroups(updatedCombination, 0);
  } else {
    // TEMPORARY RETURN - stop here to review update results
    console.log(`\n🛑 TEMPORARY RETURN - stopping here to review update results`);
  }
}

/**
 * Verify the updated groups by querying their details
 */
function verifyUpdatedGroups(updatedCombination, itemIndex) {
  if (!updatedCombination.items || itemIndex >= updatedCombination.items.length) {
    console.log(`\n--- Verification complete ---`);

    // TEMPORARY RETURN - stop here to review verification results
    console.log(`\n🛑 TEMPORARY RETURN - stopping here to review verification results`);
    return;
  }

  const item = updatedCombination.items[itemIndex];
  console.log(`\nVerifying Item ${itemIndex + 1}:`);

  if (item.inventory_group && item.inventory_group !== 0) {
    databaseConnection.obj.getById('inventory_billable_groups', item.inventory_group, function(group) {
      console.log(`  Direct inventory_group after update:`);
      console.log(`    ID: ${group.id}`);
      console.log(`    Name: "${group.name}"`);
      console.log(`    Has "(*)" in name: ${group.name.includes('(*)')}`);

      // Check choices if they exist
      if (item.choices && Array.isArray(item.choices) && item.choices.length > 0) {
        console.log(`  Checking ${item.choices.length} choices...`);
        verifyChoiceGroups(item.choices, 0, itemIndex, function() {
          // Continue with next item
          verifyUpdatedGroups(updatedCombination, itemIndex + 1);
        });
      } else {
        // Continue with next item
        verifyUpdatedGroups(updatedCombination, itemIndex + 1);
      }
    });
  } else {
    console.log(`  No direct inventory_group to verify`);

    // Check choices if they exist
    if (item.choices && Array.isArray(item.choices) && item.choices.length > 0) {
      console.log(`  Checking ${item.choices.length} choices...`);
      verifyChoiceGroups(item.choices, 0, itemIndex, function() {
        // Continue with next item
        verifyUpdatedGroups(updatedCombination, itemIndex + 1);
      });
    } else {
      // Continue with next item
      verifyUpdatedGroups(updatedCombination, itemIndex + 1);
    }
  }
}

/**
 * Verify choice groups
 */
function verifyChoiceGroups(choices, choiceIndex, itemIndex, callback) {
  if (choiceIndex >= choices.length) {
    if (callback) callback();
    return;
  }

  const choice = choices[choiceIndex];
  if (choice.inventory_group && choice.inventory_group !== 0) {
    databaseConnection.obj.getById('inventory_billable_groups', choice.inventory_group, function(group) {
      console.log(`    Choice ${choiceIndex + 1} inventory_group after update:`);
      console.log(`      ID: ${group.id}`);
      console.log(`      Name: "${group.name}"`);
      console.log(`      Has "(*)" in name: ${group.name.includes('(*)')}`);

      // Continue with next choice
      verifyChoiceGroups(choices, choiceIndex + 1, itemIndex, callback);
    });
  } else {
    // Continue with next choice
    verifyChoiceGroups(choices, choiceIndex + 1, itemIndex, callback);
  }
}
}

/**
 * Process combinations in the target category
 */
function processCombinations(groupMapping, dryRun, limit, startIndex, categoryId) {
  console.log(`\nStep 2: Processing combinations in category ${categoryId}...`);

  // Query combinations using the same structure as the XHR
  const queryObj = {
    category: categoryId,
    paged: {
      page: 0,
      pageLength: 50,
      paged: true,
      sortCol: "name",
      sortDir: "asc",
      sortCast: "string",
      count: true
    }
  };

  databaseConnection.obj.getWhere('inventory_billable_combinations', queryObj, function(combinations) {
    console.log(`Found ${combinations.length} combinations in category ${categoryId}.`);

    // Filter to only combinations with "(*)" in name
    const copyCombinations = combinations.filter(combo => combo.name.includes('(*)'));
    console.log(`Found ${copyCombinations.length} combinations with "(*)" in their names.`);

    if (copyCombinations.length === 0) {
      console.warn(`⚠️ WARNING: No combinations with "(*)" found in category ${categoryId}.`);
      return;
    }

    // Apply start index and limit
    const combinationsToProcess = copyCombinations.slice(startIndex, startIndex + limit);

    if (combinationsToProcess.length === 0) {
      console.warn(`⚠️ WARNING: No combinations to process with current settings. Check your startIndex (${startIndex}) and limit (${limit}).`);
      return;
    }

    console.log(`\nWill process ${combinationsToProcess.length} combinations:`);
    combinationsToProcess.forEach((combo, idx) => {
      console.log(`  ${idx+1}. "${combo.name}" (ID: ${combo.id})`);
    });

    // Track statistics
    const stats = {
      combinationsProcessed: 0,
      combinationsUpdated: 0,
      combinationsSkipped: 0,
      groupReferencesFound: 0,
      groupReferencesUpdated: 0,
      groupReferencesNotMapped: 0
    };

    // Process combinations sequentially
    processNextCombinationUpdate(combinationsToProcess, 0, groupMapping, dryRun, stats);
  });
}

/**
 * Process combinations one at a time to avoid server connection issues
 */
function processNextCombinationUpdate(combinations, index, groupMapping, dryRun, stats) {
  if (index >= combinations.length) {
    console.log(`\n========== GROUP REFERENCE UPDATE PROCESS COMPLETE ==========`);
    console.log(`Combinations processed: ${stats.combinationsProcessed}`);
    console.log(`Combinations updated: ${stats.combinationsUpdated}`);
    console.log(`Combinations skipped (no changes needed): ${stats.combinationsSkipped}`);
    console.log(`Group references found: ${stats.groupReferencesFound}`);
    console.log(`Group references updated: ${stats.groupReferencesUpdated}`);
    console.log(`Group references not mapped: ${stats.groupReferencesNotMapped}`);
    console.log(`==============================================================\n`);
    return;
  }

  const combination = combinations[index];
  stats.combinationsProcessed++;

  console.log(`\n========== COMBINATION ${index+1}/${combinations.length} ==========`);
  console.log(`Processing combination: "${combination.name}" (ID: ${combination.id})`);

  // Verify this combination belongs to a category with "(*)"
  if (!combination.name.includes('(*)')) {
    console.warn(`⚠️ SKIPPING: Combination "${combination.name}" does not have "(*)" in name`);
    stats.combinationsSkipped++;
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    return;
  }

  // Clone the combination for modification
  var updatedCombination = _.clone(combination);
  var hasChanges = false;

  // Process items array
  if (updatedCombination.items && Array.isArray(updatedCombination.items)) {
    console.log(`Found ${updatedCombination.items.length} items in combination.`);

    updatedCombination.items.forEach((item, itemIndex) => {
      console.log(`  Processing item ${itemIndex + 1}: "${item.name || 'Unnamed item'}"`);

      // Process choices array within each item
      if (item.choices && Array.isArray(item.choices)) {
        console.log(`    Found ${item.choices.length} choices in item ${itemIndex + 1}.`);

        item.choices.forEach((choice, choiceIndex) => {
          stats.groupReferencesFound++;

          if (choice.inventory_group && choice.inventory_group !== 0) {
            const originalGroupId = choice.inventory_group;
            console.log(`      Choice ${choiceIndex + 1}: inventory_group = ${originalGroupId}`);

            if (groupMapping[originalGroupId]) {
              const newGroupId = groupMapping[originalGroupId];
              choice.inventory_group = newGroupId;
              hasChanges = true;
              stats.groupReferencesUpdated++;
              console.log(`        ✅ UPDATED: ${originalGroupId} → ${newGroupId}`);
            } else {
              stats.groupReferencesNotMapped++;
              console.log(`        ⚠️ NOT MAPPED: Group ID ${originalGroupId} not found in mapping`);
            }
          } else {
            console.log(`      Choice ${choiceIndex + 1}: no inventory_group or inventory_group = 0`);
          }
        });
      } else {
        console.log(`    No choices array found in item ${itemIndex + 1}.`);
      }
    });
  } else {
    console.log(`No items array found in combination.`);
  }

  // Update the combination if changes were made
  if (hasChanges) {
    if (!dryRun) {
      console.log(`Updating combination "${combination.name}"...`);

      databaseConnection.obj.update('inventory_billable_combinations', updatedCombination, function() {
        stats.combinationsUpdated++;
        console.log(`✅ UPDATED: Combination "${combination.name}" (ID: ${combination.id})`);

        // Process the next combination after a short delay
        setTimeout(function() {
          processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
        }, 100);
      });
    } else {
      console.log(`[DRY RUN] Would update combination "${combination.name}" with ${stats.groupReferencesUpdated} group reference changes.`);
      stats.combinationsUpdated++;

      // Process the next combination (no delay needed for dry run)
      processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
    }
  } else {
    console.log(`No changes needed for combination "${combination.name}".`);
    stats.combinationsSkipped++;

    // Process the next combination (no delay needed)
    processNextCombinationUpdate(combinations, index + 1, groupMapping, dryRun, stats);
  }
}
